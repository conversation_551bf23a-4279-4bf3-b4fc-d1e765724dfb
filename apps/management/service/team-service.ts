/**
 * 团队服务
 *
 * 提供团队相关的API请求方法
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { createApiClient, PaginatedResponse } from '@ragtop-web/ui/lib/api'
import { API_PREFIX } from './index'

// 创建API客户端
const apiClient = createApiClient({
  apiPrefix: `${API_PREFIX}/team`
})

const pageApiClient = createApiClient({
  apiPrefix: `${API_PREFIX}/team`,
  isPaginated: true,
})

/**
 * 团队创建/更新参数
 */
export interface TeamParams {
  team_id?: string;
  admin_user_id: string;
  name: string;
}

/**
 * 用户角色
 */
export interface UserRole {
  id: string;
  name: string;
}

/**
 * 管理员信息
 */
export interface AdminUser {
  id: string;
  login_name: string;
  nick: string;
  create_time: string;
  roles: UserRole[];
}

/**
 * 团队接口
 */
export interface TeamResponse {
  id: string;
  name: string;
  admin: AdminUser;
  create_time: string;
  member_count: number;
}

/**
 * 团队成员接口
 */
export interface TeamMember {
  id: string;
  user: User;
  create_time: string;
  roles: string[];
}

export interface User {
  id: string;
  login_name: string;
}



/**
 * 获取团队列表（分页）
 *
 * @param pageNumber - 当前页码
 * @param pageSize - 每页条数
 */
export const useTeams = (pageNumber: number = 1, pageSize: number = 10,keyword?: string) => {
  return useQuery({
    queryKey: ['teams', pageNumber, pageSize,keyword],
    queryFn: () => pageApiClient.post<PaginatedResponse<TeamResponse>>('/query', {
      keyword
    }, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
  })
}

/**
 * 分页获取团队成员信息
 */


export const useTeamsMember = (pageNumber: number = 1, pageSize: number = 10, team_id: string) => {
  return useQuery({
    queryKey: ['teams', pageNumber, pageSize,team_id],
    queryFn: () => pageApiClient.post<PaginatedResponse<TeamMember>>('/query-members', {
      team_id
    }, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
  })
}

/**
 * 获取单个团队详情
 */
export const useTeam = () => {
  return useMutation({
    mutationFn: (data: { team_id: string }) =>
      apiClient.post<TeamResponse>('/describe', data),
  })
}

/**
 * 创建团队
 */
export const useCreateTeam = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (teamData: TeamParams) =>
      apiClient.post<TeamResponse>('/create', teamData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
    },
  })
}

/**
 * 更新团队
 */
export const useUpdateTeam = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (teamData: TeamParams) =>
      apiClient.post<TeamResponse>('/update', teamData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
    },
  })
}

/**
 * 删除团队
 */
export const useDeleteTeam = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { team_id: string }) =>
      apiClient.post('/delete', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
    },
  })
}

// /**
//  * 获取团队成员列表
//  */
// export const useTeamMembers = (teamId: string) => {
//   return useQuery({
//     queryKey: ['teams', teamId, 'members'],
//     queryFn: () => apiClient.get<TeamMember[]>(`${API_BASE_URL}/${teamId}/members`),
//     enabled: !!teamId,
//   })
// }

