/**
 * Management应用服务API导出文件
 *
 * 从各个服务模块导出API函数
 */

import { env } from 'next-runtime-env'

export * from './team-service'
export * from './model-service'
export * from './user-service'
export * from './auth-service'

/**
 * 获取运行时API前缀
 */
export const getApiPrefix = (): string => {
  if (typeof window === 'undefined') {
    // 服务端渲染时使用process.env
    return process.env.NEXT_PUBLIC_API_PREFIX || '/api/v1/portal-ragtop-admin'
  }

  // 客户端使用next-runtime-env
  const runtimeEnv = env('NEXT_PUBLIC_API_PREFIX')
  return runtimeEnv || process.env.NEXT_PUBLIC_API_PREFIX || '/api/v1/portal-ragtop-admin'
}

// 导出API前缀常量（保持向后兼容）
export const API_PREFIX = getApiPrefix()
