import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google"
import { PublicEnvScript } from 'next-runtime-env'

import "@ragtop-web/ui/globals.css"
import { Providers } from "@/components/providers"
import { AuthCheck } from "@/components/auth-check"

const fontSans = Geist({
  subsets: ["latin"],
  variable: "--font-sans",
})

const fontMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-mono",
})

export const metadata = {
  title: 'Ragtop - 现代化 Web 应用',
  description: '一个使用 Next.js、React 和 Tailwind CSS 构建的现代化 Web 应用程序',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <PublicEnvScript />
      </head>
      <body
        className={`${fontSans.variable} ${fontMono.variable} font-sans antialiased`}
      >
        <Providers>
          <AuthCheck>
            <div className="relative flex min-h-svh flex-col">
              <main className="flex-1">
                    {children}
              </main>
            </div>
          </AuthCheck>
        </Providers>
      </body>
    </html>
  )
}
