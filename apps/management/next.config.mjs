import { configureRuntimeEnv } from 'next-runtime-env/build/configure.js'

/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ["@ragtop-web/ui"],
  output: 'standalone',
  experimental: {
    optimizePackageImports: ["@ragtop-web/ui"],
  },
    eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
}

// 配置运行时环境变量
configureRuntimeEnv()

export default nextConfig
