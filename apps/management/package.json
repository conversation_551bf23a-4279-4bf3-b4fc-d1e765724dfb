{"name": "management", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@ragtop-web/ui": "workspace:*", "@tanstack/react-query": "5.76.1", "lucide-react": "^0.475.0", "next": "^15.3.3", "next-runtime-env": "3.3.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "2.0.3"}, "devDependencies": {"@ragtop-web/eslint-config": "workspace:^", "@ragtop-web/typescript-config": "workspace:*", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5.7.3"}}