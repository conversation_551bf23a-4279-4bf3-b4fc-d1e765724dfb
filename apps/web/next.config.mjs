import { configureRuntimeEnv } from 'next-runtime-env/build/configure.js'

/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ["@ragtop-web/ui"],
  output: 'standalone',
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.optimization.splitChunks = false
    }
    return config
  },
}

// 配置运行时环境变量
configureRuntimeEnv()

export default nextConfig
