"use client"

import { useState } from 'react'
import { Button } from '@ragtop-web/ui/components/button'
import { ChatMessage } from '@/components/chat/chat-message'
import { FileDisplayItem } from '@/components/file-display'

/**
 * 聊天功能测试页面
 */
export default function TestChatPage() {
  const [showFiles, setShowFiles] = useState(false)
  const [showReferences, setShowReferences] = useState(false)

  // 模拟文件数据
  const testFiles: FileDisplayItem[] = [
    {
      title: "产品说明书.pdf",
      type: "application/pdf",
      uri: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"
    },
    {
      title: "用户手册.docx",
      type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      uri: "https://example.com/user-manual.docx"
    },
    {
      title: "API文档.pdf",
      type: "application/pdf",
      uri: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"
    }
  ]

  // 模拟引用数据
  const testReferences = [
    {
      content: {
        chunks: [
          {
            content: "这是第一个引用的内容，来自某个PDF文档。这段内容比较长，用来测试文字换行的效果。",
            documentName: "产品说明书.pdf",
            documentId: "doc1",
            imageUri: "https://via.placeholder.com/300x200/4f46e5/ffffff?text=PDF+Document",
            docUri: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"
          },
          {
            content: "这是第二个引用的内容，包含更多详细信息。",
            documentName: "用户手册.docx",
            documentId: "doc2",
            docUri: "https://example.com/doc2.docx"
          }
        ],
        docAggs: [
          {
            docId: "doc1",
            docName: "产品说明书.pdf",
            docUri: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf",
            count: 1
          },
          {
            docId: "doc2",
            docName: "用户手册.docx",
            docUri: "https://example.com/doc2.docx",
            count: 1
          }
        ]
      }
    }
  ]

  return (
    <div className="container mx-auto p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">聊天功能测试</h1>
        
        <div className="space-y-6">
          <div className="p-4 border rounded-lg">
            <h2 className="text-xl font-semibold mb-4">功能测试</h2>
            <div className="space-x-4">
              <Button 
                onClick={() => setShowFiles(!showFiles)}
                variant={showFiles ? "default" : "outline"}
              >
                {showFiles ? "隐藏" : "显示"} 文件功能
              </Button>
              <Button 
                onClick={() => setShowReferences(!showReferences)}
                variant={showReferences ? "default" : "outline"}
              >
                {showReferences ? "隐藏" : "显示"} 引用功能
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            <h2 className="text-xl font-semibold">聊天消息示例</h2>
            
            {/* 用户消息 */}
            <ChatMessage
              role="user"
              content="请帮我查看产品相关的文档信息"
              agentName="助手"
            />

            {/* 助手回复 - 只有文本 */}
            <ChatMessage
              role="assistant"
              content="好的，我来为您查看相关文档。以下是我找到的信息：\n\n根据产品说明书，这个产品具有以下特点：\n1. 高性能处理能力\n2. 用户友好的界面设计\n3. 完善的API支持\n\n您可以查看下面的相关文档获取更多详细信息。"
              agentName="助手"
              references={showReferences ? testReferences : []}
              files={showFiles ? testFiles : []}
            />

            {/* 助手回复 - 带引用但无文件 */}
            <ChatMessage
              role="assistant"
              content="这里是一个包含引用但不包含文件的回复示例。##1$$ 根据文档中的说明，##2$$ 您可以按照以下步骤操作。"
              agentName="助手"
              references={showReferences ? testReferences : []}
              files={[]}
            />

            {/* 助手回复 - 带文件但无引用 */}
            <ChatMessage
              role="assistant"
              content="这里是一个包含文件但不包含引用的回复示例。我为您准备了以下相关文档："
              agentName="助手"
              references={[]}
              files={showFiles ? testFiles : []}
            />
          </div>

          <div className="p-4 border rounded-lg bg-blue-50">
            <h2 className="text-xl font-semibold mb-2">测试说明</h2>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>点击上方按钮可以切换显示文件和引用功能</li>
              <li>文件支持下载和在新窗口打开</li>
              <li>引用支持点击查看详细内容和图片放大</li>
              <li>PDF文件支持在抽屉中预览</li>
              <li>##1$$、##2$$ 格式会被转换为可点击的引用按钮</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
