/**
 * 团队服务
 *
 * 提供团队相关的API请求方法和团队ID管理
 */

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createWebApiClient } from "@/lib/api/web-api-client";
import { getApiPrefix } from "./index";
import { useAtom, useSetAtom } from "jotai";
import { teamIdAtom, switchTeamAtom } from "@/store/team-store";

// 创建API客户端
const apiClient = createWebApiClient({
  apiPrefix: getApiPrefix(),
});

// 成员API客户端
const memberApiClient = createWebApiClient({
  apiPrefix: `${getApiPrefix()}/member`,
});

export interface Team {
  id: string;
  roles: string[];
  title: string;
}

export interface User {
  create_time: number;
  id: string;
  nick: string;
  teams: Team[];
  username: string;
}

// 团队成员详情接口
export interface UserList {
  create_time: number;
  id: string;
  roles?: string[];
  user: User;
}

/**
 * 切换当前团队
 * 切换后会刷新页面重新获取数据
 */
export const useSwitchTeam = () => {
  const switchTeam = useSetAtom(switchTeamAtom);

  return useMutation({
    mutationFn: (team: Team) => {
      // 使用switchTeamAtom切换团队，会自动刷新页面
      switchTeam(team);
      return Promise.resolve(team);
    }
  });
};

/**
 * 获取当前选中的团队ID
 */
export const useCurrentTeamId = () => {
  // 使用Jotai获取当前团队ID
  const [teamId] = useAtom(teamIdAtom);
  return teamId;
};


/**
 * 获取用户列表（分页）
 *
 * @param pageNumber - 当前页码
 * @param pageSize - 每页条数
 * @param keyword - 搜索关键词
 */
export const useMembers = (pageNumber: number = 1, pageSize: number = 10, keyword?: string) => {
  return useQuery({
    queryKey: ['teams', pageNumber, pageSize, keyword],
    queryFn: () => memberApiClient.post<{
      records: UserList[],
      total: number,
      page_number: number,
      page_size: number
    }>('/query', {
      keyword
    }, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
  })
}

/**
 * 添加成员
 */
export const useMemberCreate = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (data: any) =>
      memberApiClient.post(`/create`, data),
     onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
    },
  });
};

/**
 * 删除成员
 */
export const useMemberDelete = () => {
    const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (data: any) =>
      memberApiClient.post(`/delete`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
    },
  });
};

/**
 * 查询某个成员详细信息
 */
export const useMemberDescribe = () => {
    const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: any) =>
      memberApiClient.post<User>(`current`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
    },
  });
};
