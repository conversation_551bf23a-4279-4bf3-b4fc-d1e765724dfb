/**
 * 模型服务
 *
 * 提供模型配置相关的API请求方法
 */

import { useQuery, useMutation } from "@tanstack/react-query";
import { getApiPrefix } from "./index";
import { createWebApiClient } from "@/lib/api/web-api-client";

// 创建API客户端
const apiClient = createWebApiClient({
  apiPrefix: `${getApiPrefix()}`,
});

// 模型接口
export interface ConfiguredModel {
  llm_factory_id: string;
  llm_name: string;
  llm_id: string;
  model_type: string;
  api_key: string;
  api_base?: string;
  base_url?: string;
}

export interface Model {
  id: string;
  name: string;
  logo: string;
  tags: string;
}

export enum ModelType {
    Chat = "CHAT",
    Embedding = "EMBEDDING",
    Rerank = "RERANK",
}

export interface ConfiguredModelList {
   api_base: string;
    llm_factory: string;
    llm_id: string;
    llm_name: string;
    model_type?: ModelType;
}

export interface ConfigurabledModel {
    llm_factory: string;
    llm_id: string;
    llm_name: string;
    model_type?: ModelType;
}

export interface LlmSettings {
  chat_id?: string
  embd_id?: string
  rerank_id?: string
}

/**
 * 获取已有的模型列表
 */
export const useExistingModels = (enable = false) => {
  return useQuery({
    queryKey: ["existingModels"],
    queryFn: () => apiClient.post<ConfiguredModelList[]>(`/llm/describe-models`),
    enabled: !!enable
  });
};

/**
 * 修改团队模型
 */
export const useLLMSettings = () => {
  return useMutation({
    mutationFn: (data: any) =>
      apiClient.post(`/team/modify-llm-settings`, data),

  });
};

/**
 * 获取当前团队模型
 */
export const useQueryLLMSettings = (enable = false) => {
   return useQuery({
    queryKey: ["llmSetting"],
    queryFn: () => apiClient.post<LlmSettings>(`/team/query-llm-settings`),
    enabled: !!enable
  });
};


