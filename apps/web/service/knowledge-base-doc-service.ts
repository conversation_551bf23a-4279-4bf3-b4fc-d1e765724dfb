/**
 * 知识库服务
 *
 * 提供知识库相关的API请求方法
 */

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createWebApiClient,
  PaginatedResponse,
} from "@/lib/api/web-api-client";
import { getApiPrefix } from "./index";

// 创建API客户端
const apiClient = createWebApiClient({
  apiPrefix: `${getApiPrefix()}/kbase-doc`,
});

// API基础路径

// 知识库接口
export interface KnowledgeBaseDoc {
  kbase_id: string;
  keyword?: string[];
  document_id: string;
}

/**
 * ModifyDocChunkingRequest
 */
export interface ModifyDocChunking {
    chunk_config: ChunkConfig;
    document_id?: string;
    team_id?: string;
    [property: string]: any;
}

/**
 * ChunkConfig
 */
export interface ChunkConfig {
    chunk_provider_id?: string;
    /**
     * 暂不支持
     */
    pdf_pages?: PageRange[];
    pdf_parser_config?: PdfParserConfig;
    [property: string]: any;
}

/**
 * PageRange，暂不支持
 */
export interface PageRange {
    end?: number;
    start?: number;
    [property: string]: any;
}

/**
 * PdfParserConfig
 */
export interface PdfParserConfig {
    parser_provider_id?: string;
    [property: string]: any;
}

/**
 * KbaseChunkConfig
 */
export interface KbaseChunkConfig {
  chunk_provider_id?: string;
  [property: string]: any;
}

/**
 * KbasePdfParserConfig
 */
export interface KbasePdfParserConfig {
  parser_provider_id?: string;
  [property: string]: any;
}

/**
 * 运行状态
 */
export enum RunStatus {
  Cancel = "CANCEL",
  Done = "DONE",
  Fail = "FAIL",
  Running = "RUNNING",
  Unstart = "UNSTART",
}


// 知识库详情接口
export interface KnowledgeBaseDocList {
  chunk_config?: ChunkConfig;
  create_time?: Date;
  /**
   * 启用状态
   */
  enable_status?: string;
  id: string;
  name: string;
  /**
   * 运行状态
   */
  run_status: RunStatus;
  size?: number;
  type?: string;
  [property: string]: any;
}

/**
 * 获取知识库详情列表
 * TODO: 分页查询
 */
export const useKnowledgeBaseDoc = (id = "", pageNumber = 1, pageSize = 10, enabled = true) => {
  return useQuery({
    queryKey: ["kbaseDoc", pageNumber, pageSize, id],
    queryFn: () =>
      apiClient.post<PaginatedResponse<KnowledgeBaseDocList>>(
        "/query",
        { kbase_id: id },
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      ),
    enabled: !!id && enabled,
  });
};

/**
 * 启用文档
 */
export const useKBaseDocEnable = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: {document_id:string}) =>
      apiClient.post(`/enable`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};

/**
 * 禁用文档
 * @returns
 */
export const useKBaseDocDisabled = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {document_id:string}) =>
      apiClient.post(`/disable`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};

/**
 * 开始解析
 * @returns
 */

export const useKBaseDocStartParse = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: {document_id:string}) =>
      apiClient.post(`/start-parse`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};

/**
 * 终止解析
 * @returns
 */
export const useKBaseDocStopParse = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: {document_id:string}) =>
      apiClient.post(`/stop-parse`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};

/**
 * 将文档从知识库中移除
 * @returns
 */
export const useKBaseDocDelete = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {document_id:string}) =>
      apiClient.post(`/delete`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};

/**
 * 修改切片方法
 */

export const useKBaseDocModifyChunking = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ModifyDocChunking) =>
      apiClient.post(`/modify-chunking-config`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  });
};
