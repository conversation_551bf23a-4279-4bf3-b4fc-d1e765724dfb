/**
 * 数据集服务
 *
 * 提供数据集相关的API请求方法
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { createWebApiClient, PaginatedResponse } from '@/lib/api/web-api-client'
import { getApiPrefix } from './index'
import { ResType } from './types'

// 创建API客户端
const apiClient = createWebApiClient({
  apiPrefix: `${getApiPrefix()}/tableset`,
})

// 数据集接口
export interface Dataset {
    create_time?: Date;
    description?: string;
    id: string;
    infra_res_id?: string;
    name?: string;
    /**
     * 资源类型
     */
    res_type?: ResType;
    [property: string]: any;
}



/**
 * DbTablesetRes，response#data
 */
export interface DbTablesetRes {
    build_version?: string;
    building_version?: string;
    create_time?: Date;
    description?: string;
    id?: string;
    infra_res_id?: string;
    meta?: Meta;
    name?: string;
    /**
     * 资源类型
     */
    res_type?: ResType;
    [property: string]: any;
}

/**
 * Meta
 */
export interface Meta {
    database?: string;
    password?: string;
    username?: string;
    [property: string]: any;
}

/**
 *  数据集创建参数
 */
export interface CreateDatasetParams {
    database?: string;
    description?: string;
    name?: string;
    password?: string;
    team_id?: string;
    username?: string;
    [property: string]: any;
}

/**
 * ModifyDbTablesetRequest
 */
export interface UpdateDatasetParams {
    description?: string;
    name?: string;
    tableset_id?: string;
    team_id?: string;
    [property: string]: any;
}

export interface TableSchema {
  annotation?: string;
    comment?: string;
    table_name?: string;
    [property: string]: any;
}
export interface DbSchema {
    annotation?: string;
    comment?: string;
    schema_name?: string;
    [property: string]: any;
}


/**
 * 修改schema注释
 */
export interface DocumentAnnotation {
    column_name?: string;
    comment?: string;
    schema_name?: string;
    table_name?: string;
    tableset_id?: string;
    team_id?: string;
    [property: string]: any;
}

/**
 * ListDbTableColumnRequest
 */
export interface DocumentColumnsParams {
    schema_name?: string;
    table_name?: string;
    tableset_id?: string;
    team_id?: string;
    [property: string]: any;
}

export interface DbTableColumn {
    annotation?: string;
    column_name?: string;
    comment?: string;
    data_type?: string;
    [property: string]: any;
}

/**
 * 获取数据集列表
 * 分页查询
 */
export const useDatasets = (pageNumber = 1, pageSize = 10, enabled = true) => {
  return useQuery({
    queryKey: ['datasets', pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<Dataset>>('/query', {}, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
    enabled,
  })
}

/**
 * 获取单个数据集
 */
export const useDataset = () => {
  return useMutation({
    mutationFn: (data: { tableset_id: string }) => apiClient.post<DbTablesetRes>(`/db/describe`, data),
  })
}

/**
 * 创建数据集
 */
export const useCreateDataset = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateDatasetParams) => apiClient.post(`/db/create`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['datasets'] })
    },
  })
}

/**
 * 更新数据集
 */
export const useUpdateDataset = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UpdateDatasetParams ) => apiClient.post(`/db/modify`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['datasets'] })
    },
  })
}

/**
 * 删除数据集
 */
export const useDeleteDataset = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { tableset_id: string }) => apiClient.post(`/delete`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['datasets'] })
    },
  })
}

/**
 * 构建数据集
 */
export const useBuildDataset = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { tableset_id: string }) => apiClient.post(`/db/build`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['datasetDocuments'] })
    },
  })
}

/**
 * 获取数据集表结构
 */
export const useDatasetSchemas = (id:string,pageNumber = 1, pageSize = 10, enabled = true) =>
  {
  return useQuery({
    queryKey: ['datasetDocuments',id, pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<DbSchema>>('/db/describe_schemas', {tableset_id:id}, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
    enabled:!!id && enabled
  })
}

/**
 * 根据表结构查询table
 */

export const useDatasetTables = (pageNumber = 1, pageSize = 10) => {
  return useMutation({
    mutationFn: (data: Omit<DocumentColumnsParams, "table_name">) => apiClient.post<PaginatedResponse<TableSchema>>(`/db/describe_tables`, data, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
  })
}

/**
 * 获取数据集表文档
 */
export const useDatasetDocuments = () => {
  return useMutation({
    mutationFn: (data: DocumentColumnsParams) => apiClient.post<DbTableColumn[]>(`/db/describe_table_columns`, data),
  })
}

/**
 * 更新文档注释
 */
export const useUpdateDocument = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: DocumentAnnotation) => apiClient.post(`/db/modify_annotation`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['datasetDocuments'] })
    },
  })
}

/**
 * 测试数据集连接
 */
export const useTestConnection = () => {
  return useMutation({
    mutationFn: (data: Omit<CreateDatasetParams, 'description' | "name">) => apiClient.post<{ success: boolean, message: string }>(`/db/test-connection`, data),
  })
}
