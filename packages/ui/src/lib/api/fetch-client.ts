/**
 * 通用fetch客户端
 *
 * 提供统一的API请求方法，包括错误处理和响应解析
 */

import { toast } from "sonner"
import { env } from 'next-runtime-env'

/**
 * API错误类
 */
export class ApiError extends Error {
  status: number
  data?: any

  constructor(message: string, status: number, data?: any) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.data = data
  }
}

/**
 * 请求配置接口
 */
export interface FetchOptions extends RequestInit {
  params?: Record<string, string | number | boolean | undefined>
  showErrorToast?: boolean
  baseUrl?: string
  apiPrefix?: string
  requireAuth?: boolean
  requireTeamId?: boolean
  isPaginated?: boolean
  pageNumber?: number
  pageSize?: number
}

/**
 * API响应接口
 */
export interface ApiResponse<T> {
  code: number | string
  data: T
  msg: string
  request_id: string
}

/**
 * 成功的API响应码
 */
export const API_SUCCESS_CODE = "SUCCESS"

/**
 * token失效的响应码
 */
export const API_BAD_CREDENTIAL_CODE = ["BAD_CREDENTIAL","CREDENTIAL_EXPIRED","INSUFFICIENT_AUTHENTICATION","USER_STATUS_INVALID"]


/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> {
  page_number: number
  page_size: number
  total: number
  records: T[]
}

/**
 * 获取存储的访问令牌
 */
export const getAccessToken = (): string | null => {
  if (typeof window === 'undefined') return null
  return localStorage.getItem('access_token')
}

/**
 * 设置访问令牌
 */
export const setAccessToken = (token: string): void => {
  if (typeof window === 'undefined') return
  localStorage.setItem('access_token', token)
}

/**
 * 清除访问令牌
 */
export const clearAccessToken = (): void => {
  if (typeof window === 'undefined') return
  localStorage.removeItem('access_token')
}

/**
 * 获取存储的团队ID
 */
export const getTeamId = (): string | null => {
  if (typeof window === 'undefined') return null
  if(localStorage.getItem('team_id')){
      return JSON.parse(localStorage.getItem('team_id') || '')
    }
    return null
}

/**
 * 获取运行时环境变量中的API基础URL
 */
export const getApiBaseUrl = (): string => {
  // 优先使用process.env，这样在构建时就能确定值
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || ""

  // 如果在客户端且next-runtime-env可用，则尝试获取运行时值
  if (typeof window !== 'undefined') {
    try {
      const runtimeEnv = env('NEXT_PUBLIC_API_BASE_URL')
      return runtimeEnv || baseUrl
    } catch (error) {
      // 如果next-runtime-env不可用，使用默认值
      return baseUrl
    }
  }

  return baseUrl
}

/**
 * 设置团队ID
 */
export const setTeamId = (teamId: string): void => {
  if (typeof window === 'undefined') return
  localStorage.setItem('team_id', teamId)
}

/**
 * 清除团队ID
 */
export const clearTeamId = (): void => {
  if (typeof window === 'undefined') return
  localStorage.removeItem('team_id')
}

/**
 * 默认的请求头
 */
const getDefaultHeaders = (requireAuth: boolean = true, isFormData: boolean = false): Record<string, string> => {
  const headers: Record<string, string> = {}

  // 只有在非 FormData 请求时才设置 Content-Type
  if (!isFormData) {
    headers['Content-Type'] = 'application/json'
  }

  if (requireAuth) {
    const token = getAccessToken()
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
  }

  return headers
}

/**
 * 通用fetch方法
 *
 * @param url - 请求URL
 * @param options - 请求配置
 * @returns 响应数据
 */
export const fetchClient = async <T>(url: string, options: FetchOptions = {}): Promise<T> => {
  const {
    showErrorToast = true,
    baseUrl = getApiBaseUrl(),
    apiPrefix = '',
    requireAuth = true,
    requireTeamId = true,
    isPaginated = false,
    pageNumber = 1,
    pageSize = 10,
    ...fetchOptions
  } = options

  // 检查是否为 FormData
  const isFormData = fetchOptions.body instanceof FormData

  // 构建API URL
  const apiUrl = `${baseUrl}${apiPrefix}${url}`

  // 合并默认请求头
  const headers = {
    ...getDefaultHeaders(requireAuth, isFormData),
    ...options.headers,
  }

  try {
    const response = await fetch(apiUrl, {
      ...fetchOptions,
      headers,
    })

    // 检查响应状态
    if (!response.ok) {
      let errorData
      try {
        errorData = await response.json()
      } catch (e) {
        errorData = { message: response.statusText }
      }
      const errorMessage = errorData.msg || errorData.message || `请求失败: ${response.status}`

      // 显示错误提示
      if (showErrorToast) {
        toast.error(errorMessage)
      }

      throw new ApiError(errorMessage, response.status, errorData)
    }

    // 解析响应数据
    let responseData: any
    if (response.headers.get('Content-Type')?.includes('application/json')) {

      responseData = await response.json()
    } else {
      responseData = await response.text()
    }

    // 处理标准API响应格式
    if (responseData && typeof responseData === 'object') {
      if ('code' in responseData &&  API_BAD_CREDENTIAL_CODE.includes(responseData.code)) {
        clearAccessToken()
        clearTeamId()

        // 清除所有认证相关的localStorage项
        localStorage.removeItem('user')
        localStorage.removeItem('currentTeam')
        localStorage.removeItem('current_user')
        localStorage.removeItem('current_team')

        // 清除所有以 ragtop- 开头的本地存储项（如果有的话）
        Object.keys(localStorage).forEach((key) => {
          if (key.startsWith("ragtop-")) {
            localStorage.removeItem(key);
          }
        });

        window.location.href = '/login'
      }

      // 检查响应码
      if ('code' in responseData && responseData.code !== API_SUCCESS_CODE && responseData.code !== 'SUCCESS') {
        const errorMessage = responseData.msg || '请求失败，服务器返回错误';
        // 显示错误提示
        if (showErrorToast) {
          toast.error(errorMessage);
        }

        throw new ApiError(errorMessage, 200, responseData);
      }

      // 成功响应，返回data部分
      return responseData.data as T;
    }

    // 非标准格式，直接返回
    return responseData as T
  } catch (error) {
    // 处理网络错误
    if (!(error instanceof ApiError) && showErrorToast) {
      toast.error('网络错误，请检查您的连接')
    }
    throw error
  }
}

/**
 * 创建API客户端
 *
 * @param baseConfig - 基础配置
 * @returns API客户端对象
 */
export const createApiClient = (baseConfig: Partial<FetchOptions> = {}) => {
  /**
   * GET请求
   */
  const get = <T>(url: string, options: FetchOptions = {}): Promise<T> => {
    return fetchClient<T>(url, { ...baseConfig, ...options, method: 'GET' })
  }

  /**
   * POST请求
   */
  const post = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
    const { isPaginated, requireTeamId = baseConfig.requireTeamId, pageNumber, pageSize, ...restOptions } = options

    // 需要排除的不添加team_id的路径
    const excludedPaths = ['/auth/signin', '/auth/signout', '/user/current']
    const shouldAddTeamId = requireTeamId && !excludedPaths.some(path => url.includes(path))

    // 检查是否为 FormData
    const isFormData = data instanceof FormData

    // 准备请求数据
    let finalData = data || {}
    let requestBody: any

    if (isFormData) {
      // 如果是 FormData，直接使用，不进行 JSON 序列化
      requestBody = data

      // 对于 FormData，不添加分页参数和团队ID到 body 中
      // 这些应该通过查询参数传递
    } else {
      // 对于普通对象，进行正常处理
      // 添加分页参数
      if (isPaginated) {
        finalData = {
          ...finalData,
          page_number: pageNumber || 1,
          page_size: pageSize || 10
        }
      }

      // 添加团队ID到请求体
      if (shouldAddTeamId) {
        const teamId = getTeamId()
        if (teamId) {
          finalData = {
            ...finalData,
            team_id: teamId
          }
        }
      }

      requestBody = Object.keys(finalData).length > 0 ? JSON.stringify(finalData) : undefined
    }

    return fetchClient<T>(url, {
      ...baseConfig,
      ...restOptions,
      method: 'POST',
      body: requestBody,
      isPaginated,
      requireTeamId: true // 已在此处处理，不需要在fetchClient中再处理
    })
  }

  /**
   * PUT请求
   */
  const put = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
    const { isPaginated, requireTeamId = baseConfig.requireTeamId, pageNumber, pageSize, ...restOptions } = options

    // 需要排除的不添加team_id的路径
    const excludedPaths = ['/auth/signin', '/auth/signout', '/user/current']
    const shouldAddTeamId = requireTeamId && !excludedPaths.some(path => url.includes(path))

    // 准备请求数据
    let finalData = data || {}

    // 添加分页参数
    if (isPaginated) {
      finalData = {
        ...finalData,
        page_number: pageNumber || 1,
        page_size: pageSize || 10
      }
    }

    // 添加团队ID到请求体
    if (shouldAddTeamId) {
      const teamId = getTeamId()
      if (teamId) {
        finalData = {
          ...finalData,
          team_id: teamId
        }
      }
    }

    return fetchClient<T>(url, {
      ...baseConfig,
      ...restOptions,
      method: 'PUT',
      body: Object.keys(finalData).length > 0 ? JSON.stringify(finalData) : undefined,
      isPaginated,
      requireTeamId: true // 已在此处处理，不需要在fetchClient中再处理
    })
  }

  /**
   * PATCH请求
   */
  const patch = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
    const { isPaginated, requireTeamId = baseConfig.requireTeamId, pageNumber, pageSize, ...restOptions } = options

    // 需要排除的不添加team_id的路径
    const excludedPaths = ['/auth/signin', '/auth/signout', '/user/current']
    const shouldAddTeamId = requireTeamId && !excludedPaths.some(path => url.includes(path))

    // 准备请求数据
    let finalData = data || {}

    // 添加分页参数
    if (isPaginated) {
      finalData = {
        ...finalData,
        page_number: pageNumber || 1,
        page_size: pageSize || 10
      }
    }

    // 添加团队ID到请求体
    if (shouldAddTeamId) {
      const teamId = getTeamId()
      if (teamId) {
        finalData = {
          ...finalData,
          team_id: teamId
        }
      }
    }

    return fetchClient<T>(url, {
      ...baseConfig,
      ...restOptions,
      method: 'PATCH',
      body: Object.keys(finalData).length > 0 ? JSON.stringify(finalData) : undefined,
      isPaginated,
      requireTeamId: true // 已在此处处理，不需要在fetchClient中再处理
    })
  }

  /**
   * DELETE请求
   */
  const del = <T>(url: string, options: FetchOptions = {}): Promise<T> => {
    return fetchClient<T>(url, { ...baseConfig, ...options, method: 'DELETE' })
  }

  return {
    get,
    post,
    put,
    patch,
    del
  }
}

// 默认导出的API客户端方法
export const get = <T>(url: string, options: FetchOptions = {}): Promise<T> => {
  return fetchClient<T>(url, { ...options, method: 'GET' })
}

export const post = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
  const { isPaginated, requireTeamId = true, pageNumber, pageSize, ...restOptions } = options

  // 需要排除的不添加team_id的路径
  const excludedPaths = ['/auth/signin', '/auth/signout', '/user/current']
  const shouldAddTeamId = requireTeamId && !excludedPaths.some(path => url.includes(path))

  // 检查是否为 FormData
  const isFormData = data instanceof FormData

  // 准备请求数据
  let finalData = data || {}
  let requestBody: any

  if (isFormData) {
    // 如果是 FormData，直接使用，不进行 JSON 序列化
    requestBody = data

    // 对于 FormData，不添加分页参数和团队ID到 body 中
    // 这些应该通过查询参数传递
  } else {
    // 对于普通对象，进行正常处理
    // 添加分页参数
    if (isPaginated) {
      finalData = {
        ...finalData,
        page_number: pageNumber || 1,
        page_size: pageSize || 10
      }
    }

    // 添加团队ID到请求体
    if (shouldAddTeamId) {
      const teamId = getTeamId()
      if (teamId) {
        finalData = {
          ...finalData,
          team_id: teamId
        }
      }
    }

    requestBody = Object.keys(finalData).length > 0 ? JSON.stringify(finalData) : undefined
  }

  return fetchClient<T>(url, {
    ...restOptions,
    method: 'POST',
    body: requestBody,
    isPaginated,
    requireTeamId: true // 已在此处处理，不需要在fetchClient中再处理
  })
}

export const put = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
  const { isPaginated, requireTeamId = true, pageNumber, pageSize, ...restOptions } = options

  // 需要排除的不添加team_id的路径
  const excludedPaths = ['/auth/signin', '/auth/signout', '/user/current']
  const shouldAddTeamId = requireTeamId && !excludedPaths.some(path => url.includes(path))

  // 准备请求数据
  let finalData = data || {}

  // 添加分页参数
  if (isPaginated) {
    finalData = {
      ...finalData,
      page_number: pageNumber || 1,
      page_size: pageSize || 10
    }
  }

  // 添加团队ID到请求体
  if (shouldAddTeamId) {
    const teamId = getTeamId()
    if (teamId) {
      finalData = {
        ...finalData,
        team_id: teamId
      }
    }
  }

  return fetchClient<T>(url, {
    ...restOptions,
    method: 'PUT',
    body: Object.keys(finalData).length > 0 ? JSON.stringify(finalData) : undefined,
    isPaginated,
    requireTeamId: true // 已在此处处理，不需要在fetchClient中再处理
  })
}

export const patch = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
  const { isPaginated, requireTeamId = true, pageNumber, pageSize, ...restOptions } = options

  // 需要排除的不添加team_id的路径
  const excludedPaths = ['/auth/signin', '/auth/signout', '/user/current']
  const shouldAddTeamId = requireTeamId && !excludedPaths.some(path => url.includes(path))

  // 准备请求数据
  let finalData = data || {}

  // 添加分页参数
  if (isPaginated) {
    finalData = {
      ...finalData,
      page_number: pageNumber || 1,
      page_size: pageSize || 10
    }
  }

  // 添加团队ID到请求体
  if (shouldAddTeamId) {
    const teamId = getTeamId()
    if (teamId) {
      finalData = {
        ...finalData,
        team_id: teamId
      }
    }
  }

  return fetchClient<T>(url, {
    ...restOptions,
    method: 'PATCH',
    body: Object.keys(finalData).length > 0 ? JSON.stringify(finalData) : undefined,
    isPaginated,
    requireTeamId: true // 已在此处处理，不需要在fetchClient中再处理
  })
}

export const del = <T>(url: string, options: FetchOptions = {}): Promise<T> => {
  return fetchClient<T>(url, { ...options, method: 'DELETE' })
}
