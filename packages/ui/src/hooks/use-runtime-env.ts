/**
 * 运行时环境变量Hook
 * 
 * 提供安全的运行时环境变量获取方法
 */

import { env } from 'next-runtime-env'
import { useEffect, useState } from 'react'

/**
 * 获取运行时环境变量的Hook
 * @param key 环境变量键名
 * @param defaultValue 默认值
 * @returns 环境变量值
 */
export const useRuntimeEnv = (key: string, defaultValue: string = '') => {
  const [value, setValue] = useState<string>(defaultValue)

  useEffect(() => {
    // 只在客户端执行
    if (typeof window !== 'undefined') {
      try {
        const runtimeValue = env(key)
        setValue(runtimeValue || defaultValue)
      } catch (error) {
        console.warn(`Failed to get runtime env ${key}:`, error)
        setValue(defaultValue)
      }
    }
  }, [key, defaultValue])

  return value
}

/**
 * 获取API基础URL的Hook
 */
export const useApiBaseUrl = () => {
  const defaultValue = process.env.NEXT_PUBLIC_API_BASE_URL || ""
  return useRuntimeEnv('NEXT_PUBLIC_API_BASE_URL', defaultValue)
}

/**
 * 获取API前缀的Hook
 */
export const useApiPrefix = (defaultPrefix: string) => {
  const defaultValue = process.env.NEXT_PUBLIC_API_PREFIX || defaultPrefix
  return useRuntimeEnv('NEXT_PUBLIC_API_PREFIX', defaultValue)
}
